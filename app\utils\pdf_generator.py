from jinja2 import Environment, FileSystemLoader
from weasyprint import HTML
import os
import json
from datetime import datetime

def safe_json_dumps(obj):
    """Safely convert object to JSON string."""
    try:
        return json.dumps(obj, indent=2, default=str)
    except (TypeError, ValueError):
        return str(obj)

async def generate_pdf(data: dict, output_path: str):
    """Generate PDF report from data."""
    template_dir = os.path.join(os.path.dirname(__file__), "..", "templates")
    env = Environment(loader=FileSystemLoader(template_dir))

    # Add custom filter for safe JSON serialization
    env.filters['safe_json'] = safe_json_dumps

    # Choose template based on data structure
    if "search" in data and "findings" in data:
        # New enhanced report format
        template = env.get_template("enhanced_report_template.html")
        html_out = template.render(
            search=data["search"],
            findings=data["findings"],
            summary=data["summary"],
            generated_at=datetime.now()
        )
    else:
        # Legacy format for backward compatibility
        template = env.get_template("report_template.html")
        html_out = template.render(data=data)

    HTML(string=html_out).write_pdf(output_path)
