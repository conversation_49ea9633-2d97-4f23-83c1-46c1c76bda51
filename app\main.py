from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.routes import scan, auth
from app.database import engine, Base

# Create database tables
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Ghostify OSINT API",
    description="Advanced OSINT investigation and reporting platform",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(scan.router)
app.include_router(auth.router)

@app.get("/")
def root():
    return {
        "message": "Ghostify OSINT API running",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
def health_check():
    return {"status": "healthy"}
