from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import asyncio
import aiohttp
import logging
import time

logger = logging.getLogger(__name__)

@dataclass
class OSINTResult:
    """Standard result format for OSINT findings."""
    source: str
    platform: str
    finding_type: str
    title: Optional[str] = None
    description: Optional[str] = None
    url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    risk_score: float = 0.0
    confidence: float = 0.0
    found_at: Optional[datetime] = None
    screenshot_path: Optional[str] = None

@dataclass
class SearchTarget:
    """Target information for OSINT searches."""
    name: Optional[str] = None
    email: Optional[str] = None
    username: Optional[str] = None
    phone: Optional[str] = None
    aliases: Optional[List[str]] = None
    location: Optional[str] = None
    image_path: Optional[str] = None

class OSINTCollector(ABC):
    """Base class for all OSINT data collectors."""
    
    def __init__(self, name: str, source_type: str):
        self.name = name
        self.source_type = source_type
        self.rate_limit = 60  # requests per minute
        self.last_request = None
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    @abstractmethod
    async def search(self, target: SearchTarget) -> List[OSINTResult]:
        """Perform OSINT search for the given target."""
        pass
    
    async def rate_limit_check(self):
        """Check and enforce rate limiting."""
        if self.last_request:
            time_since_last = time.time() - self.last_request
            min_interval = 60 / self.rate_limit  # seconds between requests
            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                await asyncio.sleep(sleep_time)
        self.last_request = time.time()
    
    def calculate_risk_score(self, finding_data: Dict[str, Any]) -> float:
        """Calculate risk score for a finding (0.0 to 10.0)."""
        # Base implementation - override in specific collectors
        base_score = 1.0
        
        # Increase score for sensitive information
        if any(keyword in str(finding_data).lower() for keyword in 
               ['password', 'breach', 'leak', 'hack', 'stolen', 'compromised']):
            base_score += 3.0
        
        if any(keyword in str(finding_data).lower() for keyword in 
               ['dark web', 'tor', 'illegal', 'criminal']):
            base_score += 5.0
        
        return min(base_score, 10.0)
    
    def calculate_confidence(self, finding_data: Dict[str, Any]) -> float:
        """Calculate confidence score for a finding (0.0 to 1.0)."""
        # Base implementation - override in specific collectors
        confidence = 0.5
        
        # Higher confidence for verified sources
        if 'verified' in str(finding_data).lower():
            confidence += 0.3
        
        # Higher confidence for recent data
        if 'recent' in str(finding_data).lower():
            confidence += 0.2
        
        return min(confidence, 1.0)

class OSINTEngine:
    """Main engine that coordinates all OSINT collectors."""
    
    def __init__(self):
        self.collectors: List[OSINTCollector] = []
        self.results: List[OSINTResult] = []
    
    def register_collector(self, collector: OSINTCollector):
        """Register a new OSINT collector."""
        self.collectors.append(collector)
        logger.info(f"Registered collector: {collector.name}")
    
    async def search_all(self, target: SearchTarget) -> List[OSINTResult]:
        """Run all registered collectors against the target."""
        all_results = []
        
        for collector in self.collectors:
            try:
                logger.info(f"Running collector: {collector.name}")
                async with collector:
                    results = await collector.search(target)
                    all_results.extend(results)
                    logger.info(f"Collector {collector.name} found {len(results)} results")
            except Exception as e:
                logger.error(f"Error in collector {collector.name}: {str(e)}")
                continue
        
        self.results = all_results
        return all_results
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of all findings."""
        if not self.results:
            return {}
        
        total_findings = len(self.results)
        avg_risk_score = sum(r.risk_score for r in self.results) / total_findings
        
        # Count by source type
        source_counts = {}
        for result in self.results:
            source_counts[result.source] = source_counts.get(result.source, 0) + 1
        
        # Count high-risk findings
        high_risk_count = sum(1 for r in self.results if r.risk_score >= 7.0)
        
        return {
            "total_findings": total_findings,
            "average_risk_score": round(avg_risk_score, 2),
            "high_risk_findings": high_risk_count,
            "source_breakdown": source_counts,
            "risk_level": self._determine_risk_level(avg_risk_score, high_risk_count)
        }
    
    def _determine_risk_level(self, avg_risk: float, high_risk_count: int) -> str:
        """Determine overall risk level."""
        if avg_risk >= 7.0 or high_risk_count >= 3:
            return "critical"
        elif avg_risk >= 5.0 or high_risk_count >= 1:
            return "high"
        elif avg_risk >= 3.0:
            return "medium"
        else:
            return "low"
