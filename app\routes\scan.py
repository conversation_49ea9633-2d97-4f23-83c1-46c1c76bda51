from fastapi import APIRouter, UploadFile, Form, Depends, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import Optional, List
from app.database import get_db
from app.models import User, Search, Finding, Report
from app.schemas import SearchCreate, Search as SearchSchema, SearchSummary
from app.auth import get_optional_user
from app.utils.pdf_generator import generate_pdf
from app.osint.base import OSINTEngine, SearchTarget
from app.osint.email_breach import HaveIBeenPwnedCollector, EmailRepCollector
from app.osint.social_media import <PERSON><PERSON>oll<PERSON>, GoogleDorksCollector
from app.osint.phone_lookup import <PERSON>NumberCollector, ReversePhoneLookupCollector
from app.connectors.manager import connector_manager
import uuid, os
import asyncio
from datetime import datetime

router = APIRouter()

# Initialize OSINT engine
osint_engine = OSINTEngine()
osint_engine.register_collector(HaveIBeenPwnedCollector())
osint_engine.register_collector(EmailRepCollector())
osint_engine.register_collector(SherlockCollector())
osint_engine.register_collector(GoogleDorksCollector())
osint_engine.register_collector(PhoneNumberCollector())
osint_engine.register_collector(ReversePhoneLookupCollector())

async def execute_connector_scan(search_id: str, scan_data: dict, db: Session):
    """Execute OSINT scan using the connector framework"""
    try:
        # Execute scan using connector manager
        results = await connector_manager.execute_scan(scan_data)

        # Update search status
        search = db.query(Search).filter(Search.id == search_id).first()
        if search:
            search.status = "completed"
            search.exposure_score = min(100, results.get('total_findings', 0) * 2)  # Simple scoring

            # Store findings
            aggregated_results = results.get('results', {})

            # Store social media accounts
            for account in aggregated_results.get('social_media_accounts', []):
                finding = Finding(
                    search_id=search_id,
                    source=account.get('connector', 'unknown'),
                    finding_type="social_media_account",
                    data={
                        'platform': account.get('properties', {}).get('platform'),
                        'username': account.get('properties', {}).get('username'),
                        'profile_url': account.get('properties', {}).get('profile_url'),
                        'confidence': account.get('confidence', 0.0)
                    },
                    risk_level="medium" if account.get('confidence', 0) > 0.7 else "low"
                )
                db.add(finding)

            # Store other findings
            for category in ['emails', 'usernames', 'domains', 'locations']:
                for item in aggregated_results.get(category, []):
                    finding = Finding(
                        search_id=search_id,
                        source=item.get('connector', 'unknown'),
                        finding_type=category.rstrip('s'),  # Remove plural
                        data=item,
                        risk_level="low"
                    )
                    db.add(finding)

            db.commit()

    except Exception as e:
        # Update search status to error
        search = db.query(Search).filter(Search.id == search_id).first()
        if search:
            search.status = "failed"
            db.commit()

@router.get("/api/connectors")
async def list_connectors():
    """List available OSINT connectors"""
    try:
        connectors = connector_manager.get_available_connectors()
        return {
            "status": "success",
            "connectors": connectors,
            "total": len(connectors)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing connectors: {str(e)}")

@router.post("/api/scan/v2", response_model=dict)
async def create_scan_v2(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_user),
    name: Optional[str] = Form(None),
    username: Optional[str] = Form(None),
    email: Optional[str] = Form(None),
    phone: Optional[str] = Form(None),
    aliases: Optional[str] = Form(None),
    location: Optional[str] = Form(None),
    image: Optional[UploadFile] = None
):
    """Create a new OSINT scan using the connector framework"""
    try:
        # Generate search ID
        search_id = str(uuid.uuid4())

        # Prepare scan data
        scan_data = {
            "name": name,
            "username": username,
            "email": email,
            "phone": phone,
            "aliases": aliases,
            "location": location
        }

        # Remove None values
        scan_data = {k: v for k, v in scan_data.items() if v is not None}

        if not scan_data:
            raise HTTPException(status_code=400, detail="At least one search parameter is required")

        # Create search record
        search = Search(
            id=search_id,
            target_name=name,
            target_username=username,
            target_email=email,
            target_phone=phone,
            target_aliases=aliases.split(',') if aliases else None,
            target_location=location,
            status="running",
            user_id=current_user.id if current_user else None
        )
        db.add(search)
        db.commit()

        # Execute scan in background using connector framework
        background_tasks.add_task(execute_connector_scan, search_id, scan_data, db)

        return {
            "search_id": search_id,
            "status": "started",
            "message": "OSINT scan started using connector framework"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting scan: {str(e)}")

@router.post("/api/scan", response_model=dict)
async def create_scan(
    background_tasks: BackgroundTasks,
    name: Optional[str] = Form(None),
    username: Optional[str] = Form(None),
    email: Optional[str] = Form(None),
    phone: Optional[str] = Form(None),
    aliases: Optional[str] = Form(None),  # Comma-separated
    location: Optional[str] = Form(None),
    image: Optional[UploadFile] = None,
    current_user: Optional[User] = Depends(get_optional_user),
    db: Session = Depends(get_db)
):
    """Create a new OSINT scan."""

    # Validate that at least one identifier is provided
    if not any([name, username, email, phone]):
        raise HTTPException(
            status_code=400,
            detail="At least one identifier (name, username, email, or phone) is required"
        )

    # Parse aliases
    aliases_list = []
    if aliases:
        aliases_list = [alias.strip() for alias in aliases.split(",") if alias.strip()]

    # Handle image upload
    image_path = None
    if image:
        # Save uploaded image
        upload_dir = "/tmp/uploads"
        os.makedirs(upload_dir, exist_ok=True)
        image_path = os.path.join(upload_dir, f"{uuid.uuid4()}_{image.filename}")

        with open(image_path, "wb") as buffer:
            content = await image.read()
            buffer.write(content)

    # Create search record
    search = Search(
        user_id=current_user.id if current_user else None,
        target_name=name,
        target_email=email,
        target_username=username,
        target_phone=phone,
        target_aliases=aliases_list,
        target_location=location,
        image_path=image_path,
        status="pending"
    )

    db.add(search)
    db.commit()
    db.refresh(search)

    # Start background scan
    background_tasks.add_task(perform_osint_scan, search.id, db)

    return {
        "search_id": search.id,
        "status": "pending",
        "message": "OSINT scan started. Check status for updates."
    }

async def perform_osint_scan(search_id: str, db: Session):
    """Perform the actual OSINT scan in the background."""

    # Get search record
    search = db.query(Search).filter(Search.id == search_id).first()
    if not search:
        return

    try:
        # Update status to running
        search.status = "running"
        db.commit()

        # Create search target
        target = SearchTarget(
            name=search.target_name,
            email=search.target_email,
            username=search.target_username,
            phone=search.target_phone,
            aliases=search.target_aliases,
            location=search.target_location,
            image_path=search.image_path
        )

        # Run OSINT collectors
        results = await osint_engine.search_all(target)

        # Save findings to database
        for result in results:
            # Ensure metadata is JSON serializable
            metadata = result.metadata
            if metadata is not None:
                try:
                    import json
                    # Test if it's serializable
                    json.dumps(metadata, default=str)
                except (TypeError, ValueError) as e:
                    # Convert to string if not serializable
                    metadata = {"error": f"Non-serializable data: {str(metadata)[:200]}..."}
                    print(f"Warning: Converting non-serializable metadata to string: {e}")

            finding = Finding(
                search_id=search_id,
                source=result.source,
                platform=result.platform,
                finding_type=result.finding_type,
                title=result.title,
                description=result.description,
                url=result.url,
                screenshot_path=result.screenshot_path,
                finding_metadata=metadata,
                risk_score=result.risk_score,
                confidence=result.confidence,
                found_at=result.found_at
            )
            db.add(finding)

        # Calculate overall risk assessment
        summary = osint_engine.get_summary()
        search.exposure_score = summary.get("average_risk_score", 0.0)
        search.risk_level = summary.get("risk_level", "low")
        search.status = "completed"
        search.completed_at = datetime.now()

        db.commit()

        # Generate PDF report
        await generate_enhanced_report(search_id, db)

    except Exception as e:
        # Mark search as failed
        search.status = "failed"
        db.commit()

        # Log the error with more details
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in OSINT scan for search {search_id}: {str(e)}")
        print(f"Full traceback: {error_details}")

        # You could also log to a file or monitoring system here

async def generate_enhanced_report(search_id: str, db: Session):
    """Generate enhanced PDF report with all findings."""

    search = db.query(Search).filter(Search.id == search_id).first()
    if not search:
        return

    findings = db.query(Finding).filter(Finding.search_id == search_id).all()

    # Prepare report data
    report_data = {
        "search": search,
        "findings": findings,
        "summary": {
            "total_findings": len(findings),
            "exposure_score": search.exposure_score,
            "risk_level": search.risk_level,
            "breach_count": len([f for f in findings if f.finding_type == "data_breach"]),
            "social_profiles": len([f for f in findings if f.finding_type == "social_profile"]),
            "dark_web_mentions": any(f.risk_score >= 7.0 for f in findings)
        }
    }

    # Generate PDF
    output_path = f"/tmp/report_{search_id}.pdf"
    await generate_pdf(report_data, output_path)

    # Create report record
    report = Report(
        search_id=search_id,
        file_path=output_path
    )
    db.add(report)
    db.commit()

@router.get("/api/scan/{search_id}/status")
async def get_scan_status(
    search_id: str,
    db: Session = Depends(get_db)
):
    """Get the status of a scan."""

    search = db.query(Search).filter(Search.id == search_id).first()
    if not search:
        raise HTTPException(status_code=404, detail="Search not found")

    findings_count = db.query(Finding).filter(Finding.search_id == search_id).count()

    return {
        "search_id": search_id,
        "status": search.status,
        "exposure_score": search.exposure_score,
        "risk_level": search.risk_level,
        "findings_count": findings_count,
        "created_at": search.created_at,
        "completed_at": search.completed_at
    }

@router.get("/api/scan/{search_id}/results")
async def get_scan_results(
    search_id: str,
    db: Session = Depends(get_db)
):
    """Get detailed results of a scan."""

    search = db.query(Search).filter(Search.id == search_id).first()
    if not search:
        raise HTTPException(status_code=404, detail="Search not found")

    findings = db.query(Finding).filter(Finding.search_id == search_id).all()

    return {
        "search": search,
        "findings": [
            {
                "id": f.id,
                "source": f.source,
                "platform": f.platform,
                "finding_type": f.finding_type,
                "title": f.title,
                "description": f.description,
                "url": f.url,
                "risk_score": f.risk_score,
                "confidence": f.confidence,
                "metadata": f.finding_metadata,
                "found_at": f.found_at
            }
            for f in findings
        ]
    }

@router.get("/api/report/{search_id}")
async def get_report(search_id: str, db: Session = Depends(get_db)):
    """Download the PDF report for a scan."""

    report = db.query(Report).filter(Report.search_id == search_id).first()
    if not report or not os.path.exists(report.file_path):
        raise HTTPException(status_code=404, detail="Report not found")

    return FileResponse(
        report.file_path,
        media_type="application/pdf",
        filename=f"ghostify-report-{search_id}.pdf"
    )
