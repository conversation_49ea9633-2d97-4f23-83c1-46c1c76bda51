#!/usr/bin/env python3
"""
Ghostify CLI - Command Line Interface for OSINT Operations
Provides comprehensive CLI access to all Ghostify functionality
"""

import asyncio
import argparse
import json
import sys
import os
import time
from typing import Optional, List, Dict, Any
from pathlib import Path
import colorama
from colorama import Fore, Back, Style

# Add the app directory to the path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from schemas import BatchSearchCreate, BatchIdentifierInput, CLIConfig, CLIResult, RiskScore
from connectors.manager import connector_manager
from connectors.entities import PersonEntity, EmailEntity, UsernameEntity, PhoneEntity
from connectors.base import EntityType
from utils.pdf_generator import generate_pdf

# Initialize colorama for cross-platform colored output
colorama.init()

class GhostifyCLI:
    """Main CLI class for Ghostify OSINT operations"""
    
    def __init__(self):
        self.config = CLIConfig()
        self.start_time = None
        
    def print_banner(self):
        """Print the Ghostify CLI banner"""
        if not self.config.no_color:
            print(f"{Fore.CYAN}")
        
        banner = """
    ╔═══════════════════════════════════════════════════════════════╗
    ║                                                               ║
    ║   ██████╗ ██╗  ██╗ ██████╗ ███████╗████████╗██╗███████╗██╗   ║
    ║  ██╔════╝ ██║  ██║██╔═══██╗██╔════╝╚══██╔══╝██║██╔════╝██║   ║
    ║  ██║  ███╗███████║██║   ██║███████╗   ██║   ██║█████╗  ██║   ║
    ║  ██║   ██║██╔══██║██║   ██║╚════██║   ██║   ██║██╔══╝  ╚═╝   ║
    ║  ╚██████╔╝██║  ██║╚██████╔╝███████║   ██║   ██║██║     ██╗   ║
    ║   ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝   ╚═╝   ╚═╝╚═╝     ╚═╝   ║
    ║                                                               ║
    ║              Advanced OSINT Investigation Platform            ║
    ║                        CLI Interface v2.0                    ║
    ║                                                               ║
    ╚═══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        
        if not self.config.no_color:
            print(f"{Style.RESET_ALL}")
    
    def print_status(self, message: str, status_type: str = "info"):
        """Print colored status messages"""
        if self.config.no_color:
            print(f"[{status_type.upper()}] {message}")
            return
            
        colors = {
            "info": Fore.BLUE,
            "success": Fore.GREEN,
            "warning": Fore.YELLOW,
            "error": Fore.RED,
            "progress": Fore.CYAN
        }
        
        color = colors.get(status_type, Fore.WHITE)
        print(f"{color}[{status_type.upper()}]{Style.RESET_ALL} {message}")
    
    def print_progress_bar(self, current: int, total: int, prefix: str = "Progress"):
        """Print a progress bar"""
        if self.config.no_color:
            percentage = (current / total) * 100
            print(f"{prefix}: {current}/{total} ({percentage:.1f}%)")
            return
            
        bar_length = 50
        filled_length = int(bar_length * current // total)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        percentage = (current / total) * 100
        
        print(f"\r{Fore.CYAN}{prefix}: |{bar}| {current}/{total} ({percentage:.1f}%){Style.RESET_ALL}", end='')
        if current == total:
            print()  # New line when complete
    
    async def scan_target(self, args) -> CLIResult:
        """Execute OSINT scan on target"""
        self.start_time = time.time()
        
        try:
            # Prepare scan data from arguments
            scan_data = self._prepare_scan_data(args)
            
            if not scan_data:
                return CLIResult(
                    success=False,
                    message="No valid identifiers provided",
                    error="At least one identifier (name, email, username, phone) is required"
                )
            
            self.print_status(f"Starting OSINT scan with {len(scan_data)} identifiers", "info")
            
            # List available connectors
            connectors = connector_manager.get_available_connectors()
            self.print_status(f"Available connectors: {len(connectors)}", "info")
            
            if self.config.verbose:
                for connector in connectors:
                    self.print_status(f"  • {connector['name']} v{connector['version']}", "info")
            
            # Execute scan
            results = await connector_manager.execute_scan(scan_data)
            
            # Calculate execution time
            execution_time = time.time() - self.start_time
            
            # Process results
            processed_results = self._process_scan_results(results, execution_time)
            
            # Output results
            await self._output_results(processed_results, args)
            
            self.print_status(f"Scan completed in {execution_time:.2f} seconds", "success")
            
            return CLIResult(
                success=True,
                message="Scan completed successfully",
                data=processed_results,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - self.start_time if self.start_time else 0
            self.print_status(f"Scan failed: {str(e)}", "error")
            
            return CLIResult(
                success=False,
                message="Scan failed",
                error=str(e),
                execution_time=execution_time
            )
    
    def _prepare_scan_data(self, args) -> Dict[str, Any]:
        """Prepare scan data from CLI arguments"""
        scan_data = {}
        
        # Single identifiers
        if args.name:
            scan_data['name'] = args.name
        if args.email:
            scan_data['email'] = args.email
        if args.username:
            scan_data['username'] = args.username
        if args.phone:
            scan_data['phone'] = args.phone
        if args.location:
            scan_data['location'] = args.location
            
        # Batch identifiers
        if args.emails:
            scan_data['emails'] = args.emails
        if args.usernames:
            scan_data['usernames'] = args.usernames
        if args.phones:
            scan_data['phones'] = args.phones
        if args.aliases:
            scan_data['aliases'] = args.aliases
            
        return scan_data
    
    def _process_scan_results(self, results: Dict[str, Any], execution_time: float) -> Dict[str, Any]:
        """Process and enhance scan results"""
        processed = {
            'execution_time': execution_time,
            'total_findings': results.get('total_findings', 0),
            'results_by_connector': results.get('results', {}),
            'risk_score': self._calculate_risk_score(results),
            'summary': self._generate_summary(results)
        }
        
        return processed
    
    def _calculate_risk_score(self, results: Dict[str, Any]) -> RiskScore:
        """Calculate comprehensive risk score"""
        total_findings = results.get('total_findings', 0)
        
        # Simple scoring algorithm (can be enhanced)
        base_score = min(total_findings * 5, 100)  # 5 points per finding, max 100
        
        # Adjust based on finding types
        category_scores = {}
        risk_factors = []
        
        for connector, connector_results in results.get('results', {}).items():
            findings_count = len(connector_results.get('findings', []))
            category_scores[connector] = min(findings_count * 10, 100)
            
            if findings_count > 0:
                risk_factors.append(f"{findings_count} findings from {connector}")
        
        # Determine risk level
        if base_score >= 80:
            risk_level = "critical"
        elif base_score >= 60:
            risk_level = "high"
        elif base_score >= 30:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        recommendations = self._generate_recommendations(base_score, category_scores)
        
        return RiskScore(
            overall_score=base_score,
            category_scores=category_scores,
            risk_level=risk_level,
            risk_factors=risk_factors,
            recommendations=recommendations
        )
    
    def _generate_recommendations(self, score: float, category_scores: Dict[str, float]) -> List[str]:
        """Generate security recommendations based on findings"""
        recommendations = []
        
        if score >= 60:
            recommendations.append("Immediate action required - high exposure detected")
            recommendations.append("Review and secure all identified accounts")
            recommendations.append("Enable two-factor authentication where possible")
        
        if 'email_breach' in category_scores and category_scores['email_breach'] > 50:
            recommendations.append("Change passwords for breached email accounts")
            recommendations.append("Monitor for suspicious activity")
        
        if 'social_media' in category_scores and category_scores['social_media'] > 30:
            recommendations.append("Review social media privacy settings")
            recommendations.append("Consider reducing public information exposure")
        
        if score < 30:
            recommendations.append("Maintain current security practices")
            recommendations.append("Regular monitoring recommended")
        
        return recommendations
    
    def _generate_summary(self, results: Dict[str, Any]) -> str:
        """Generate executive summary"""
        total_findings = results.get('total_findings', 0)
        connectors_used = len(results.get('results', {}))
        
        summary = f"OSINT scan completed using {connectors_used} connectors. "
        summary += f"Total findings: {total_findings}. "
        
        if total_findings == 0:
            summary += "No significant exposure detected."
        elif total_findings < 5:
            summary += "Low exposure detected."
        elif total_findings < 15:
            summary += "Moderate exposure detected."
        else:
            summary += "High exposure detected - immediate review recommended."
        
        return summary
