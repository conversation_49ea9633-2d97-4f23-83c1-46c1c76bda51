#!/usr/bin/env python3
"""
Test script for Ghostify Connector Framework
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.connectors.manager import connector_manager
from app.connectors.entities import UsernameEntity
from app.connectors.base import EntityType

async def test_connectors():
    """Test the connector framework"""
    print("🦆 Ghostify Connector Framework Test")
    print("=" * 50)
    
    # List available connectors
    print("\n📋 Available Connectors:")
    connectors = connector_manager.get_available_connectors()
    for connector in connectors:
        print(f"  • {connector['name']} v{connector['version']}")
        print(f"    {connector['description']}")
        print(f"    Input types: {', '.join(connector['supported_input_types'])}")
        print(f"    Output types: {', '.join(connector['output_types'])}")
        print(f"    Tags: {', '.join(connector['tags'])}")
        print()
    
    # Test with a sample username
    test_username = "testuser123"
    print(f"\n🔍 Testing with username: {test_username}")
    
    # Create test scan data
    scan_data = {
        "username": test_username,
        "name": "Test User",
        "email": "<EMAIL>"
    }
    
    print("\n⚡ Executing scan...")
    try:
        results = await connector_manager.execute_scan(scan_data)
        
        print(f"\n✅ Scan completed!")
        print(f"Status: {results['status']}")
        print(f"Execution time: {results['execution_time']:.2f} seconds")
        print(f"Entities processed: {results['entities_processed']}")
        print(f"Connectors executed: {results['connectors_executed']}")
        print(f"Total findings: {results['total_findings']}")
        
        # Show connector stats
        print(f"\n📊 Connector Statistics:")
        for connector, stats in results['connector_stats'].items():
            print(f"  • {connector}: {stats['executed']} executed, {stats['found']} found results")
        
        # Show aggregated results
        aggregated = results['results']
        print(f"\n🎯 Results Summary:")
        print(f"  • Social Media Accounts: {len(aggregated['social_media_accounts'])}")
        print(f"  • Emails: {len(aggregated['emails'])}")
        print(f"  • Usernames: {len(aggregated['usernames'])}")
        print(f"  • Domains: {len(aggregated['domains'])}")
        
        # Show some sample findings
        if aggregated['social_media_accounts']:
            print(f"\n🔗 Sample Social Media Accounts Found:")
            for account in aggregated['social_media_accounts'][:5]:  # Show first 5
                platform = account.get('properties', {}).get('platform', 'Unknown')
                username = account.get('properties', {}).get('username', 'Unknown')
                url = account.get('properties', {}).get('profile_url', 'No URL')
                confidence = account.get('confidence', 0.0)
                print(f"  • {platform}: {username} (confidence: {confidence:.2f})")
                print(f"    URL: {url}")
        
        print(f"\n🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error during scan: {e}")
        import traceback
        traceback.print_exc()

async def test_individual_connector():
    """Test individual connector directly"""
    print("\n🧪 Testing Sherlock Connector Directly")
    print("-" * 40)
    
    try:
        # Get Sherlock connector
        sherlock = connector_manager.registry.get_connector("sherlock")
        if not sherlock:
            print("❌ Sherlock connector not found")
            return
        
        # Create test entity
        username_entity = UsernameEntity("testuser123")
        
        print(f"Testing with entity: {username_entity.value}")
        print(f"Entity type: {username_entity.entity_type.value}")
        
        # Check if connector can process this entity
        can_process = sherlock.can_process(username_entity)
        print(f"Can process: {can_process}")
        
        if can_process:
            print("Executing connector...")
            result = await sherlock.execute(username_entity, site_limit=5)  # Limit to 5 sites for testing
            
            print(f"✅ Execution completed!")
            print(f"Found {result.entity_count} entities")
            print(f"Risk level: {result.risk_level.value}")
            print(f"Execution time: {result.execution_time:.2f} seconds")
            
            if result.target_entities:
                print("Found accounts:")
                for entity in result.target_entities:
                    platform = entity.properties.get('platform', 'Unknown')
                    url = entity.properties.get('profile_url', 'No URL')
                    print(f"  • {platform}: {url}")
        
    except Exception as e:
        print(f"❌ Error testing individual connector: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Starting connector tests...")
    
    # Run tests
    asyncio.run(test_connectors())
    asyncio.run(test_individual_connector())
    
    print("\n🏁 All tests completed!")
