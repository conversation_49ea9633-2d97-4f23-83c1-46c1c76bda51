import aiohttp
import logging
from typing import List, Dict, Any
from datetime import datetime
from app.osint.base import OSINTCollector, OSINTResult, SearchTarget
import re
import json

logger = logging.getLogger(__name__)

class SherlockCollector(OSINTCollector):
    """Username search across multiple platforms (Sherlock-style)."""
    
    def __init__(self):
        super().__init__("Sherlock", "social_media")
        self.rate_limit = 30  # Conservative rate limiting
        
        # Platform definitions (simplified version of <PERSON>'s data)
        self.platforms = {
            "GitHub": {
                "url": "https://github.com/{}",
                "errorType": "status_code",
                "errorCode": 404
            },
            "Instagram": {
                "url": "https://www.instagram.com/{}",
                "errorType": "message",
                "errorMsg": "The link you followed may be broken"
            },
            "Twitter": {
                "url": "https://twitter.com/{}",
                "errorType": "message", 
                "errorMsg": "This account doesn't exist"
            },
            "LinkedIn": {
                "url": "https://www.linkedin.com/in/{}",
                "errorType": "status_code",
                "errorCode": 404
            },
            "Reddit": {
                "url": "https://www.reddit.com/user/{}",
                "errorType": "message",
                "errorMsg": "page not found"
            },
            "YouTube": {
                "url": "https://www.youtube.com/user/{}",
                "errorType": "status_code",
                "errorCode": 404
            },
            "TikTok": {
                "url": "https://www.tiktok.com/@{}",
                "errorType": "message",
                "errorMsg": "Couldn't find this account"
            },
            "Pinterest": {
                "url": "https://www.pinterest.com/{}",
                "errorType": "status_code",
                "errorCode": 404
            },
            "Tumblr": {
                "url": "https://{}.tumblr.com",
                "errorType": "status_code",
                "errorCode": 404
            },
            "Medium": {
                "url": "https://medium.com/@{}",
                "errorType": "status_code",
                "errorCode": 404
            }
        }
    
    async def search(self, target: SearchTarget) -> List[OSINTResult]:
        """Search for usernames across social media platforms."""
        results = []
        
        usernames = []
        if target.username:
            usernames.append(target.username)
        if target.aliases:
            usernames.extend(target.aliases)
        
        if not usernames:
            return results
        
        for username in usernames:
            for platform_name, platform_config in self.platforms.items():
                try:
                    await self.rate_limit_check()
                    result = await self._check_platform(username, platform_name, platform_config)
                    if result:
                        results.append(result)
                except Exception as e:
                    logger.error(f"Error checking {platform_name} for {username}: {str(e)}")
        
        return results
    
    async def _check_platform(self, username: str, platform_name: str, config: Dict[str, Any]) -> OSINTResult:
        """Check if username exists on a specific platform."""
        url = config["url"].format(username)
        
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        try:
            async with self.session.get(url, headers=headers, timeout=10) as response:
                content = await response.text()
                
                # Check for existence based on platform configuration
                exists = False
                
                if config["errorType"] == "status_code":
                    exists = response.status != config["errorCode"]
                elif config["errorType"] == "message":
                    exists = config["errorMsg"].lower() not in content.lower()
                
                if exists:
                    # Try to extract additional profile information
                    profile_info = self._extract_profile_info(content, platform_name)
                    
                    return OSINTResult(
                        source="Sherlock",
                        platform=platform_name,
                        finding_type="social_profile",
                        title=f"{platform_name} Profile Found",
                        description=f"Username '{username}' found on {platform_name}",
                        url=url,
                        metadata={
                            "username": username,
                            "platform": platform_name,
                            "profile_info": profile_info,
                            "response_status": response.status
                        },
                        risk_score=2.0,  # Social media presence is low risk by default
                        confidence=0.8,
                        found_at=datetime.now()
                    )
        
        except Exception as e:
            logger.debug(f"Error checking {platform_name}: {str(e)}")
        
        return None
    
    def _extract_profile_info(self, content: str, platform: str) -> Dict[str, Any]:
        """Extract basic profile information from page content."""
        info = {}
        
        try:
            # Extract title
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', content, re.IGNORECASE)
            if title_match:
                info["page_title"] = title_match.group(1).strip()
            
            # Extract meta description
            desc_match = re.search(r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']', content, re.IGNORECASE)
            if desc_match:
                info["description"] = desc_match.group(1).strip()
            
            # Platform-specific extractions
            if platform == "GitHub":
                # Extract repository count, followers, etc.
                repo_match = re.search(r'(\d+)\s*repositories', content, re.IGNORECASE)
                if repo_match:
                    info["repositories"] = int(repo_match.group(1))
                
                followers_match = re.search(r'(\d+)\s*followers', content, re.IGNORECASE)
                if followers_match:
                    info["followers"] = int(followers_match.group(1))
            
            elif platform == "LinkedIn":
                # Extract professional title
                title_match = re.search(r'<h1[^>]*>([^<]+)</h1>', content)
                if title_match:
                    info["professional_title"] = title_match.group(1).strip()
        
        except Exception as e:
            logger.debug(f"Error extracting profile info: {str(e)}")
        
        return info

class GoogleDorksCollector(OSINTCollector):
    """Search using Google dorks for mentions and documents."""
    
    def __init__(self):
        super().__init__("GoogleDorks", "search_engine")
        self.rate_limit = 10  # Conservative rate limiting for Google
    
    async def search(self, target: SearchTarget) -> List[OSINTResult]:
        """Search using Google dorks."""
        results = []
        
        # Build search queries
        queries = self._build_dork_queries(target)
        
        for query in queries:
            try:
                await self.rate_limit_check()
                search_results = await self._google_search(query)
                
                for search_result in search_results:
                    result = OSINTResult(
                        source="GoogleDorks",
                        platform="google",
                        finding_type="search_result",
                        title=search_result.get("title", "Google Search Result"),
                        description=search_result.get("snippet", ""),
                        url=search_result.get("url", ""),
                        metadata={
                            "query": query,
                            "search_result": search_result
                        },
                        risk_score=self._calculate_dork_risk(query, search_result),
                        confidence=0.6,
                        found_at=datetime.now()
                    )
                    results.append(result)
            
            except Exception as e:
                logger.error(f"Error with Google dork '{query}': {str(e)}")
        
        return results
    
    def _build_dork_queries(self, target: SearchTarget) -> List[str]:
        """Build Google dork queries based on target information."""
        queries = []
        
        if target.email:
            queries.extend([
                f'"{target.email}"',
                f'"{target.email}" site:pastebin.com',
                f'"{target.email}" site:github.com',
                f'"{target.email}" filetype:pdf',
                f'"{target.email}" "password" OR "leak" OR "breach"'
            ])
        
        if target.username:
            queries.extend([
                f'"{target.username}" site:github.com',
                f'"{target.username}" site:reddit.com',
                f'"{target.username}" "contact" OR "email"'
            ])
        
        if target.name:
            queries.extend([
                f'"{target.name}" site:linkedin.com',
                f'"{target.name}" filetype:pdf',
                f'"{target.name}" "resume" OR "cv"'
            ])
        
        if target.phone:
            queries.extend([
                f'"{target.phone}"',
                f'"{target.phone}" site:whitepages.com'
            ])
        
        return queries[:10]  # Limit to prevent rate limiting
    
    async def _google_search(self, query: str) -> List[Dict[str, Any]]:
        """Perform Google search (simplified - would need proper API in production)."""
        # Note: This is a simplified implementation
        # In production, you'd use Google Custom Search API or similar
        
        results = []
        
        # For demo purposes, return mock results
        # In real implementation, use Google Custom Search API
        mock_result = {
            "title": f"Search result for: {query}",
            "snippet": f"Mock search result snippet for query: {query}",
            "url": f"https://example.com/search?q={query.replace(' ', '+')}"
        }
        results.append(mock_result)
        
        return results
    
    def _calculate_dork_risk(self, query: str, result: Dict[str, Any]) -> float:
        """Calculate risk score for a dork result."""
        base_score = 2.0
        
        # Higher risk for sensitive keywords
        sensitive_keywords = ["password", "leak", "breach", "hack", "dump", "database"]
        for keyword in sensitive_keywords:
            if keyword in query.lower() or keyword in result.get("snippet", "").lower():
                base_score += 2.0
        
        # Higher risk for certain file types
        if "filetype:pdf" in query or ".pdf" in result.get("url", ""):
            base_score += 1.0
        
        # Higher risk for paste sites
        if "pastebin" in result.get("url", ""):
            base_score += 3.0
        
        return min(base_score, 10.0)
