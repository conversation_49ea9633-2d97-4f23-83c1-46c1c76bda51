from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Float, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import uuid

class User(Base):
    __tablename__ = "users"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String, unique=True, index=True, nullable=False)
    username = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    searches = relationship("Search", back_populates="user")

class Search(Base):
    __tablename__ = "searches"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=True)  # Allow anonymous searches
    
    # Search parameters
    target_name = Column(String)
    target_email = Column(String)
    target_username = Column(String)
    target_phone = Column(String)
    target_aliases = Column(JSON)  # List of aliases
    target_location = Column(String)
    image_path = Column(String)  # Path to uploaded image
    
    # Search metadata
    status = Column(String, default="pending")  # pending, running, completed, failed
    exposure_score = Column(Float)
    risk_level = Column(String)  # low, medium, high, critical
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User", back_populates="searches")
    findings = relationship("Finding", back_populates="search")
    reports = relationship("Report", back_populates="search")

class Finding(Base):
    __tablename__ = "findings"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    search_id = Column(String, ForeignKey("searches.id"), nullable=False)
    
    # Finding details
    source = Column(String, nullable=False)  # social_media, breach, phone_lookup, etc.
    platform = Column(String)  # facebook, linkedin, haveibeenpwned, etc.
    finding_type = Column(String)  # profile, breach, mention, document, etc.
    
    # Content
    title = Column(String)
    description = Column(Text)
    url = Column(String)
    screenshot_path = Column(String)
    finding_metadata = Column(JSON)  # Additional structured data
    
    # Risk assessment
    risk_score = Column(Float)
    confidence = Column(Float)  # How confident we are in this finding
    
    # Timestamps
    found_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    search = relationship("Search", back_populates="findings")

class Report(Base):
    __tablename__ = "reports"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    search_id = Column(String, ForeignKey("searches.id"), nullable=False)
    
    # Report details
    file_path = Column(String, nullable=False)
    file_format = Column(String, default="pdf")
    
    # Sharing
    is_shared = Column(Boolean, default=False)
    share_token = Column(String, unique=True)
    share_expires_at = Column(DateTime(timezone=True))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    search = relationship("Search", back_populates="reports")

class DataSource(Base):
    __tablename__ = "data_sources"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, unique=True, nullable=False)
    source_type = Column(String, nullable=False)  # social_media, breach_db, search_engine, etc.
    is_active = Column(Boolean, default=True)
    api_endpoint = Column(String)
    rate_limit = Column(Integer)  # requests per minute
    last_used = Column(DateTime(timezone=True))
    
    # Configuration
    config = Column(JSON)  # API keys, settings, etc.
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
