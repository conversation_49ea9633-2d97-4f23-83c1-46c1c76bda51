from pydantic import BaseModel, EmailStr
from typing import Optional, List, Dict, Any
from datetime import datetime

# User schemas
class UserBase(BaseModel):
    email: EmailStr
    username: str
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class User(UserBase):
    id: str
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

# Search schemas
class SearchCreate(BaseModel):
    target_name: Optional[str] = None
    target_email: Optional[EmailStr] = None
    target_username: Optional[str] = None
    target_phone: Optional[str] = None
    target_aliases: Optional[List[str]] = None
    target_location: Optional[str] = None

class SearchUpdate(BaseModel):
    status: Optional[str] = None
    exposure_score: Optional[float] = None
    risk_level: Optional[str] = None
    completed_at: Optional[datetime] = None

class Search(BaseModel):
    id: str
    user_id: Optional[str]
    target_name: Optional[str]
    target_email: Optional[str]
    target_username: Optional[str]
    target_phone: Optional[str]
    target_aliases: Optional[List[str]]
    target_location: Optional[str]
    image_path: Optional[str]
    status: str
    exposure_score: Optional[float]
    risk_level: Optional[str]
    created_at: datetime
    completed_at: Optional[datetime]
    
    class Config:
        from_attributes = True

# Finding schemas
class FindingCreate(BaseModel):
    search_id: str
    source: str
    platform: Optional[str] = None
    finding_type: str
    title: Optional[str] = None
    description: Optional[str] = None
    url: Optional[str] = None
    screenshot_path: Optional[str] = None
    finding_metadata: Optional[Dict[str, Any]] = None
    risk_score: Optional[float] = None
    confidence: Optional[float] = None
    found_at: Optional[datetime] = None

class Finding(BaseModel):
    id: str
    search_id: str
    source: str
    platform: Optional[str]
    finding_type: str
    title: Optional[str]
    description: Optional[str]
    url: Optional[str]
    screenshot_path: Optional[str]
    finding_metadata: Optional[Dict[str, Any]]
    risk_score: Optional[float]
    confidence: Optional[float]
    found_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Report schemas
class ReportCreate(BaseModel):
    search_id: str
    file_format: str = "pdf"

class Report(BaseModel):
    id: str
    search_id: str
    file_path: str
    file_format: str
    is_shared: bool
    share_token: Optional[str]
    share_expires_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Token schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# Search result summary
class SearchSummary(BaseModel):
    search_id: str
    target_info: Dict[str, Any]
    exposure_score: float
    risk_level: str
    findings_count: int
    breach_count: int
    social_media_count: int
    dark_web_mentions: bool
    completed_at: datetime
