from pydantic import BaseModel, EmailStr, validator, <PERSON>
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import re

# User schemas
class UserBase(BaseModel):
    email: EmailStr
    username: str
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class User(UserBase):
    id: str
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True

# Enhanced input validation schemas
class IdentifierInput(BaseModel):
    """Single identifier input with validation"""
    value: str
    type: str  # 'name', 'email', 'username', 'phone', 'alias'
    confidence: Optional[float] = 1.0
    source: Optional[str] = "user_input"

class BatchIdentifierInput(BaseModel):
    """Batch input for multiple identifiers"""
    names: Optional[List[str]] = Field(default_factory=list)
    emails: Optional[List[str]] = Field(default_factory=list)
    usernames: Optional[List[str]] = Field(default_factory=list)
    phones: Optional[List[str]] = Field(default_factory=list)
    aliases: Optional[List[str]] = Field(default_factory=list)
    locations: Optional[List[str]] = Field(default_factory=list)

    @validator('emails')
    def validate_emails(cls, v):
        if v:
            email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
            for email in v:
                if not email_pattern.match(email):
                    raise ValueError(f'Invalid email format: {email}')
        return v

    @validator('phones')
    def validate_phones(cls, v):
        if v:
            phone_pattern = re.compile(r'^[\+]?[1-9][\d]{0,15}$')
            for phone in v:
                cleaned_phone = re.sub(r'[^\d\+]', '', phone)
                if not phone_pattern.match(cleaned_phone):
                    raise ValueError(f'Invalid phone format: {phone}')
        return v

# Enhanced Search schemas
class SearchCreate(BaseModel):
    target_name: Optional[str] = None
    target_email: Optional[EmailStr] = None
    target_username: Optional[str] = None
    target_phone: Optional[str] = None
    target_aliases: Optional[List[str]] = None
    target_location: Optional[str] = None
    profile_photo_url: Optional[str] = None

class BatchSearchCreate(BaseModel):
    """Enhanced search creation with batch input support"""
    batch_identifiers: Optional[BatchIdentifierInput] = None
    individual_identifiers: Optional[List[IdentifierInput]] = None
    target_name: Optional[str] = None
    target_email: Optional[EmailStr] = None
    target_username: Optional[str] = None
    target_phone: Optional[str] = None
    target_aliases: Optional[List[str]] = None
    target_location: Optional[str] = None
    profile_photo_url: Optional[str] = None
    scan_options: Optional[Dict[str, bool]] = Field(default_factory=lambda: {
        'social_media': True,
        'email_breaches': True,
        'phone_lookup': True,
        'reverse_image': True,
        'google_dorks': True,
        'data_brokers': True,
        'dark_web': False
    })

    @validator('scan_options')
    def validate_scan_options(cls, v):
        valid_options = {
            'social_media', 'email_breaches', 'phone_lookup',
            'reverse_image', 'google_dorks', 'data_brokers', 'dark_web'
        }
        if v:
            for key in v.keys():
                if key not in valid_options:
                    raise ValueError(f'Invalid scan option: {key}')
        return v

class SearchUpdate(BaseModel):
    status: Optional[str] = None
    exposure_score: Optional[float] = None
    risk_level: Optional[str] = None
    completed_at: Optional[datetime] = None

class Search(BaseModel):
    id: str
    user_id: Optional[str]
    target_name: Optional[str]
    target_email: Optional[str]
    target_username: Optional[str]
    target_phone: Optional[str]
    target_aliases: Optional[List[str]]
    target_location: Optional[str]
    image_path: Optional[str]
    status: str
    exposure_score: Optional[float]
    risk_level: Optional[str]
    created_at: datetime
    completed_at: Optional[datetime]
    
    class Config:
        from_attributes = True

# Finding schemas
class FindingCreate(BaseModel):
    search_id: str
    source: str
    platform: Optional[str] = None
    finding_type: str
    title: Optional[str] = None
    description: Optional[str] = None
    url: Optional[str] = None
    screenshot_path: Optional[str] = None
    finding_metadata: Optional[Dict[str, Any]] = None
    risk_score: Optional[float] = None
    confidence: Optional[float] = None
    found_at: Optional[datetime] = None

class Finding(BaseModel):
    id: str
    search_id: str
    source: str
    platform: Optional[str]
    finding_type: str
    title: Optional[str]
    description: Optional[str]
    url: Optional[str]
    screenshot_path: Optional[str]
    finding_metadata: Optional[Dict[str, Any]]
    risk_score: Optional[float]
    confidence: Optional[float]
    found_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Report schemas
class ReportCreate(BaseModel):
    search_id: str
    file_format: str = "pdf"

class Report(BaseModel):
    id: str
    search_id: str
    file_path: str
    file_format: str
    is_shared: bool
    share_token: Optional[str]
    share_expires_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Token schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# Risk scoring schemas
class RiskScore(BaseModel):
    """Comprehensive risk scoring"""
    overall_score: float = Field(ge=0, le=100, description="Overall vulnerability score 0-100")
    category_scores: Dict[str, float] = Field(default_factory=dict)
    risk_level: str = Field(default="low", description="low, medium, high, critical")
    risk_factors: List[str] = Field(default_factory=list)
    recommendations: List[str] = Field(default_factory=list)

    @validator('risk_level')
    def validate_risk_level(cls, v):
        valid_levels = ['low', 'medium', 'high', 'critical']
        if v not in valid_levels:
            raise ValueError(f'Risk level must be one of: {valid_levels}')
        return v

class ProgressUpdate(BaseModel):
    """Progress tracking for scans"""
    search_id: str
    current_step: str
    total_steps: int
    completed_steps: int
    percentage: float = Field(ge=0, le=100)
    estimated_time_remaining: Optional[int] = None  # seconds
    current_connector: Optional[str] = None
    status_message: Optional[str] = None

# Enhanced Search result summary
class SearchSummary(BaseModel):
    search_id: str
    target_info: Dict[str, Any]
    exposure_score: float
    risk_level: str
    risk_score: Optional[RiskScore] = None
    findings_count: int
    breach_count: int
    social_media_count: int
    phone_lookup_count: int = 0
    reverse_image_count: int = 0
    google_dorks_count: int = 0
    data_broker_count: int = 0
    dark_web_mentions: bool
    completed_at: datetime
    scan_duration: Optional[float] = None  # seconds

class EnhancedReportData(BaseModel):
    """Enhanced report data structure"""
    search_summary: SearchSummary
    risk_assessment: RiskScore
    findings_by_category: Dict[str, List[Dict[str, Any]]]
    timeline: List[Dict[str, Any]]
    recommendations: List[str]
    executive_summary: str
    technical_details: Dict[str, Any]

# CLI-specific schemas
class CLIConfig(BaseModel):
    """Configuration for CLI operations"""
    output_format: str = Field(default="json", description="json, csv, txt, pdf")
    output_file: Optional[str] = None
    verbose: bool = False
    no_color: bool = False
    timeout: int = Field(default=300, description="Timeout in seconds")
    max_concurrent: int = Field(default=5, description="Max concurrent connectors")
    save_screenshots: bool = False

class CLIResult(BaseModel):
    """CLI command result"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
