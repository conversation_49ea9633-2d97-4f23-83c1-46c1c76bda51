"""
Email Analysis Connector for Ghostify
Extracts usernames and domains from email addresses
"""

import re
from typing import List
from datetime import datetime

from .base import BaseConnector, ConnectorConfig, ConnectorResult, ConnectorEntity, EntityType, RiskLevel
from .entities import EmailEntity, UsernameEntity, DomainEntity


class EmailAnalysisConnector(BaseConnector):
    """
    Email analysis connector that extracts usernames and domains from email addresses
    """
    
    @classmethod
    def get_config(cls) -> ConnectorConfig:
        return ConnectorConfig(
            name="email_analysis",
            description="Extract usernames and domains from email addresses for further investigation",
            version="1.0.0",
            author="Ghostify Team",
            enabled=True,
            rate_limit=100,  # High rate limit since this is local processing
            timeout=5,
            requires_api_key=False,
            supported_input_types=[EntityType.EMAIL, EntityType.PERSON],
            output_types=[EntityType.USERNAME, EntityType.DOMAIN],
            tags=["email", "username", "domain", "analysis"]
        )
    
    def can_process(self, entity: ConnectorEntity) -> bool:
        """Check if this connector can process the given entity"""
        if entity.entity_type == EntityType.EMAIL:
            return True
        elif entity.entity_type == EntityType.PERSON:
            # Can process person if they have an email property
            return 'email' in entity.properties
        return False
    
    async def execute(self, entity: ConnectorEntity, **kwargs) -> ConnectorResult:
        """Execute email analysis"""
        start_time = datetime.utcnow()
        
        # Extract email from entity
        if entity.entity_type == EntityType.EMAIL:
            email = entity.value
        elif entity.entity_type == EntityType.PERSON:
            email = entity.properties.get('email')
            if not email:
                return self.create_result(entity, [], error="No email found in person entity")
        else:
            return self.create_result(entity, [], error="Unsupported entity type")
        
        # Validate email format
        if not self._is_valid_email(email):
            return self.create_result(entity, [], error="Invalid email format")
        
        try:
            # Extract components
            target_entities = []
            
            # Extract username (part before @)
            username = email.split('@')[0]
            if username:
                username_entity = UsernameEntity(
                    username=username,
                    source_email=email,
                    confidence=0.9
                )
                target_entities.append(username_entity)
            
            # Extract domain (part after @)
            domain = email.split('@')[1]
            if domain:
                domain_entity = DomainEntity(
                    domain=domain,
                    source_email=email,
                    confidence=1.0
                )
                target_entities.append(domain_entity)
            
            # Check for common patterns
            risk_level = self._assess_risk(email, username, domain)
            
            # Calculate execution time
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Create result
            result = ConnectorResult(
                source_entity=entity,
                target_entities=target_entities,
                metadata={
                    'email_analyzed': email,
                    'username_extracted': username,
                    'domain_extracted': domain,
                    'email_patterns': self._analyze_patterns(email),
                    'execution_time': execution_time
                },
                risk_level=risk_level,
                confidence=0.95,
                execution_time=execution_time
            )
            
            self.logger.info(f"Email analysis extracted {len(target_entities)} entities from '{email}'")
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing email analysis: {e}")
            return self.create_result(
                entity, 
                [], 
                error=str(e),
                execution_time=(datetime.utcnow() - start_time).total_seconds()
            )
    
    def _is_valid_email(self, email: str) -> bool:
        """Validate email format"""
        if not email or '@' not in email:
            return False
        
        # Basic email validation
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    def _assess_risk(self, email: str, username: str, domain: str) -> RiskLevel:
        """Assess risk level based on email characteristics"""
        risk_factors = 0
        
        # Check for suspicious patterns
        suspicious_domains = [
            'tempmail.org', '10minutemail.com', 'guerrillamail.com',
            'mailinator.com', 'throwaway.email', 'temp-mail.org'
        ]
        
        if domain.lower() in suspicious_domains:
            risk_factors += 2
        
        # Check for suspicious username patterns
        if any(pattern in username.lower() for pattern in ['admin', 'test', 'fake', 'temp']):
            risk_factors += 1
        
        # Check for numbers-only username
        if username.isdigit():
            risk_factors += 1
        
        # Check for very short or very long usernames
        if len(username) < 3 or len(username) > 30:
            risk_factors += 1
        
        # Determine risk level
        if risk_factors >= 3:
            return RiskLevel.HIGH
        elif risk_factors >= 2:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _analyze_patterns(self, email: str) -> dict:
        """Analyze email patterns for additional insights"""
        username, domain = email.split('@')
        
        patterns = {
            'has_numbers': bool(re.search(r'\d', username)),
            'has_dots': '.' in username,
            'has_underscores': '_' in username,
            'has_hyphens': '-' in username,
            'username_length': len(username),
            'domain_parts': len(domain.split('.')),
            'is_common_provider': domain.lower() in [
                'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
                'aol.com', 'icloud.com', 'protonmail.com'
            ],
            'is_business_domain': not domain.lower() in [
                'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
                'aol.com', 'icloud.com', 'protonmail.com'
            ]
        }
        
        return patterns
